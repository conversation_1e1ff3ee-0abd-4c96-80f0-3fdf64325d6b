package dev.step.app.androidplatform.biz.ad.admob.nat1ve

import android.content.Context
import com.google.android.gms.ads.AdListener
import com.google.android.gms.ads.AdLoader
import com.google.android.gms.ads.AdRequest
import com.google.android.gms.ads.LoadAdError
import com.google.android.gms.ads.nativead.NativeAd
import com.google.android.gms.ads.nativead.NativeAdOptions
import dev.step.app.androidplatform.androidcomponent.global.debugLog
import dev.step.app.androidplatform.biz.SplashHelper
import dev.step.app.androidplatform.biz.ad.admob.AdmobAdUnitIds
import dev.step.app.androidplatform.biz.analytics.AnalyticsLogEvent
import dev.step.app.androidplatform.biz.analytics.logEventRecord
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.GlobalScope
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.first
import kotlinx.coroutines.flow.update
import kotlinx.coroutines.launch
import org.koin.core.annotation.Single

@Single
class AdmobNativeAdManager(
    private val context: Context,
    private val splashController: SplashHelper,
) {
    @Suppress("PrivatePropertyName")
    private val TAG = "NativeAdManager"
    private val adUnitNameLowercase = "native"

    private val adKey = AdmobAdUnitIds.NATIVE

    private val adFlowMap = mutableMapOf<NativeAdPlace, MutableStateFlow<NativeAd?>>()
    private val adStateFlowMap = mutableMapOf<NativeAdPlace, MutableStateFlow<AdmobNativeAdState>>()


    fun getAdFlow(place: NativeAdPlace): MutableStateFlow<NativeAd?> {
        return adFlowMap.getOrPut(place) {
            MutableStateFlow(null)
        }
    }


    fun buildAd(place: NativeAdPlace) {
        val adBuilder = AdLoader.Builder(context, adKey)

        adBuilder.forNativeAd { newNativeAd ->
            GlobalScope.launch(Dispatchers.Main.immediate) {
                val adFlow = getAdFlow(place)
                val cacheNativeAd = adFlow.first()

                cacheNativeAd?.destroy()

                newNativeAd.setOnPaidEventListener { adValue ->
                    val adSourceName =
                        newNativeAd.responseInfo?.loadedAdapterResponseInfo?.adSourceName
                    val adFormat = "native"
                    val adUnitId = adKey

                    AnalyticsLogEvent.tryToRecordTotalAdsRevenue001(adValue, adSourceName)
                    AnalyticsLogEvent.recordAdImpressionRevenue(
                        adValue,
                        adSourceName,
                        adFormat,
                        adFormat + "_" + place.named
                    )
                    AnalyticsLogEvent.recordAdImpression(adValue, adSourceName, adFormat, adUnitId)
                    AnalyticsLogEvent.tenjinEventAdImpressionAdMob(adValue, newNativeAd)
                }
                adFlow.update { newNativeAd }
            }
        }

        adBuilder.withNativeAdOptions(
            NativeAdOptions.Builder().setAdChoicesPlacement(
                NativeAdOptions.ADCHOICES_BOTTOM_RIGHT
            ).build()
        )

        val adLoader = adBuilder.withAdListener(object : AdListener() {
            override fun onAdFailedToLoad(loadAdError: LoadAdError) {
                // todo try once
                debugLog(tag = TAG) { "onAdFailedToLoad" }
            }

            override fun onAdLoaded() {
                logEventRecord("ad_${adUnitNameLowercase}_load_success")
            }

            override fun onAdClicked() {
                debugLog(tag = TAG) { "place:${place.name} onAdClicked()" }
//                logEventRecord("ad_native_click")
                // should use onAdOpened() replace onAdClicked(). cuz sometimes onAdClicked() will not get callback
            }

            override fun onAdOpened() {
                debugLog(tag = TAG) { "place:${place.name} onAdOpened()" }
                splashController.doSkipSplash(true)
                logEventRecord("ad_${adUnitNameLowercase}_click")
            }

            override fun onAdImpression() {
                logEventRecord("ad_${adUnitNameLowercase}_impress")
            }

        }).build()

        adLoader.loadAd(AdRequest.Builder().build())

        logEventRecord("ad_${adUnitNameLowercase}_load")
    }

    fun destroy(place: NativeAdPlace) {
        GlobalScope.launch(Dispatchers.Main.immediate) {
            adFlowMap[place]?.first()?.destroy()
            adFlowMap[place]?.update { null }
        }
    }

    fun destroyAll() {
        GlobalScope.launch(Dispatchers.Main.immediate) {
            adFlowMap.keys.forEach(::destroy)
        }
    }
}
