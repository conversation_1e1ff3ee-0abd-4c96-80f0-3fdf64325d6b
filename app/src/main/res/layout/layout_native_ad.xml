<?xml version="1.0" encoding="utf-8"?>
<androidx.cardview.widget.CardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    app:cardBackgroundColor="#89E6FF"
    app:cardCornerRadius="2dp"
    app:cardElevation="0dp">

    <com.google.android.gms.ads.nativead.NativeAdView
        android:id="@+id/native_ad"
        android:layout_width="match_parent"
        android:layout_height="wrap_content">

        <LinearLayout
            android:id="@+id/ll_ad_text"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="4dp"
            android:layout_marginTop="4dp"
            android:elevation="1px"
            android:gravity="center_vertical"
            android:orientation="horizontal">

            <androidx.cardview.widget.CardView
                android:scaleY="0.95"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                app:cardCornerRadius="2dp"
                app:cardElevation="0dp"
                app:cardUseCompatPadding="false">

                <TextView
                    android:id="@+id/tvAd"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:background="#999"
                    android:paddingVertical="1dp"
                    android:paddingHorizontal="2dp"
                    android:text=" AD "
                    android:textAppearance="@style/TextAppearance.AppCompat.Body2"
                    android:textColor="@android:color/white"
                    android:textSize="9.3sp" />
            </androidx.cardview.widget.CardView>


            <TextView
                android:id="@+id/ad_advertiser"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="2dp"
                android:textAppearance="@style/TextAppearance.AppCompat.Body1"
                android:textSize="10sp"
                tools:text="Advertiser" />
        </LinearLayout>

        <FrameLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginHorizontal="8dp"
            android:layout_marginTop="10dp"
            android:layout_marginBottom="6dp">


            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal">

                <com.google.android.gms.ads.nativead.MediaView
                    android:layout_weight="1"
                    android:id="@+id/ad_media"
                    android:layout_width="0dp"
                    android:layout_height="122dp"
                    tools:background="@color/cardview_dark_background" />

                <androidx.constraintlayout.widget.ConstraintLayout
                    android:layout_weight="1"
                    android:id="@+id/ll_ad_content_action"
                    android:layout_width="0dp"
                    android:layout_height="match_parent"
                    android:layout_marginStart="6dp">

                    <androidx.cardview.widget.CardView
                        android:id="@+id/mcv_icon_image_view"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        app:cardCornerRadius="4dp"
                        app:cardElevation="0dp"
                        app:cardUseCompatPadding="false"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toTopOf="parent">

                        <ImageView
                            android:id="@+id/ad_app_icon"
                            android:layout_width="32dp"
                            android:layout_height="32dp"

                            tools:src="@drawable/logo" />

                    </androidx.cardview.widget.CardView>


                    <TextView
                        android:id="@+id/ad_headline"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="4dp"
                        android:ellipsize="end"
                        android:gravity="center_vertical"
                        android:maxLines="2"
                        android:textAppearance="@style/TextAppearance.AppCompat.Title"
                        android:textSize="13sp"
                        android:textStyle="bold"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintStart_toEndOf="@+id/mcv_icon_image_view"
                        app:layout_constraintTop_toTopOf="@+id/mcv_icon_image_view"
                        app:layout_constraintBottom_toBottomOf="@+id/mcv_icon_image_view"
                        tools:text="Title" />

                    <TextView
                        android:maxLines="4"
                        android:id="@+id/ad_body"
                        android:layout_width="0dp"
                        android:layout_height="0dp"
                        android:layout_marginBottom="1dp"
                        android:layout_marginTop="1dp"
                        android:ellipsize="end"
                        android:textAppearance="@style/TextAppearance.AppCompat.Body1"
                        android:textSize="11sp"
                        app:layout_constraintBottom_toTopOf="@+id/card_ad_call_to_action_container"
                        app:layout_constraintEnd_toEndOf="parent"
                        android:layout_marginStart="2dp"
                        app:layout_constraintStart_toStartOf="@+id/mcv_icon_image_view"
                        app:layout_constraintTop_toBottomOf="@id/mcv_icon_image_view"
                        tools:text="BodyBodyBodyBodyBodyBodyBodyBodyBodyBodyBodyBodyBodyBodyBodyBodyBodyBodyBodyBodyBodyBodyBodyBodyBodyBodyBodyBodyBodyBodyBodyBodyBodyBodyBodyBodyBodyBody" />

                    <androidx.cardview.widget.CardView
                        android:id="@+id/card_ad_call_to_action_container"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginHorizontal="3dp"
                        app:cardCornerRadius="100dp"
                        app:cardElevation="0dp"
                        app:layout_constraintBottom_toBottomOf="parent"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintStart_toStartOf="parent">

                        <Button
                            android:id="@+id/ad_call_to_action"
                            android:layout_width="match_parent"
                            android:layout_height="28dp"
                            android:background="#FFF"
                            android:elevation="0dp"
                            android:minWidth="80dp"
                            android:textColor="#000"
                            android:textSize="13dp"
                            tools:text="Click" />
                    </androidx.cardview.widget.CardView>

                </androidx.constraintlayout.widget.ConstraintLayout>
            </LinearLayout>
        </FrameLayout>

    </com.google.android.gms.ads.nativead.NativeAdView>
</androidx.cardview.widget.CardView>